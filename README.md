# Flinkk CRM

A comprehensive CRM system for travel and hospitality businesses.

## Recent Updates

### Room Booking Save Functionality with Cart Metadata Sync

**Overview**: Enhanced the existing Room Booking save operation to also update the associated cart's metadata in a single atomic operation. This ensures that destination and hotel-related information is consistently reflected in both the room booking and cart context.

**Key Features**:
- **Atomic Operations**: Room booking save and cart metadata update happen in sequence with proper error handling
- **Metadata Synchronization**: Automatically syncs destination and hotel information to cart metadata
- **Fail-Safe Design**: Room booking save succeeds even if cart metadata update fails
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

**Technical Implementation**:

1. **Enhanced API Endpoint**: `POST /api/room-bookings/save`
   - Accepts additional fields: `destination_id`, `destination_name`, `hotel_id`, `hotel_name`, `check_in_date`, `check_out_date`
   - Updates cart metadata after successful room booking save
   - Returns metadata update status in response

2. **New FlinkkInventoryAPI Method**: `updateCartMetadata()`
   - Updates cart metadata via Medusa Storefront API
   - Supports destination and hotel information
   - Uses proper error handling and logging

3. **Updated Hook**: `useSaveRoomBookings`
   - Accepts destination and hotel parameters
   - Passes metadata to API endpoint
   - Logs metadata update status

**Example Usage**:
```typescript
const result = await saveRoomBookings({
  roomBlocks,
  cartId,
  hotelId: "hotel_123",
  hotelName: "Grand Hotel",
  destinationId: "dest_456",
  destinationName: "Paris, France",
  checkInDate: "2024-01-15",
  checkOutDate: "2024-01-20",
  pricingData
});
```

**Response Format**:
```json
{
  "success": true,
  "message": "2 room bookings added to cart",
  "data": { /* cart data */ },
  "cartMetadataUpdated": true,
  "cartMetadataError": null
}
```

**Error Handling**:
- If cart metadata update fails, the room booking save still succeeds
- Detailed error logging for debugging
- Graceful degradation ensures system reliability

## Getting Started

### Prerequisites

- Node.js 18+
- npm 8+
- PostgreSQL

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-username/flinkk-crm.git
cd flinkk-crm
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
# Create a .env file in the root directory based on the provided .env.example file
cp .env.example .env
# Edit the file to point to your backend API
NEXT_PUBLIC_BACKEND_URL=https://api.yourdomain.com
```

4. Set up the database:

```bash
npm run prisma generate
npm run prisma db push
```

### Development Mode

To run the application in development mode:

```bash
npm run dev
```

This starts the Next.js frontend application.

### Server Ports

- Frontend Server: [http://localhost:3008](http://localhost:3008)
- Copilot Example: [http://localhost:3000](http://localhost:3000)
- Slides App: [http://localhost:3011](http://localhost:3011)
- Socket.io Server: [http://localhost:3000](http://localhost:3000) (when running separately)

### Production Mode

#### Building Applications

To build all applications:

```bash
npm run build
```

To build a specific application:

```bash
npm --filter frontend build
```

#### Running in Production

To run all applications in production mode:

```bash
npm start
```

To run a specific application in production mode:

```bash
npm --filter frontend start
```

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Database Schema Visualization

This project includes tools to visualize the database schema. You can use the following commands:

```bash
# Generate and view Entity-Relationship Diagram (ERD)
npm run generate-erd

# Generate and view DBML schema
npm run generate-dbml

# Launch Prisma Studio for interactive schema exploration
npm run studio
```

For more details, see [SCHEMA-VISUALIZATION.md](./SCHEMA-VISUALIZATION.md).

## Real-Time Features

This project includes real-time messaging capabilities powered by a separate socket-server. The socket-server is intentionally excluded from the monorepo structure and should be managed separately. It uses Azure Web PubSub for Socket.IO to provide scalable real-time communication.

### Socket Server

The socket-server is located in the `socket-server/` directory but is not part of the Turborepo workspace. It has its own package.json and build process. To work with the socket-server:

```bash
# Start the socket server in development mode
npm run dev:socket

# Start both the frontend and socket server
npm run dev:all

# Build the socket server
cd socket-server && npm run build

# Start the socket server in production mode
npm start:socket

# Start both the frontend and socket server in production mode
npm start:all
```

## Monorepo Structure

This project is organized as a monorepo using Turborepo. The structure is as follows:

- **apps/frontend**: Next.js frontend application
- **packages/shared**: Shared utilities, types, and configurations

### Working with the Monorepo

Turborepo allows you to run commands across all packages or target specific packages:

```bash
# Run a command in all packages
npm run dev

# Run a command in a specific package
npm --filter frontend dev

# Build all packages
npm run build

# Build a specific package
npm --filter frontend build
```

For more information on working with Turborepo, see the [Turborepo documentation](https://turbo.build/repo/docs).

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
