import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import { useEffect, useState } from "react";
import { cn } from "@flinkk/lib/cn";
import { FormElementInstance } from "../types";
import {
  FormItem,
  FormMessage,
  FormLabel,
  FormDescription,
  FormField,
  FormControl,
} from "@flinkk/components/ui/form";
import React from "react";
import { FieldPath, FieldValues, useWatch } from "react-hook-form";
import { DynamicSelectFieldReactHookFormProps } from "./shared-types";
import { useDynamicSelect } from "@flinkk/hooks/mutation/use-dynamic-select";
import { Loader2 } from "lucide-react";

const validate = (formElement: FormElementInstance, currentValue: string) => {
  const element = formElement as any;
  if (element.extraAttributes.required) {
    return currentValue.length > 0;
  }
  return true;
};

// Legacy props interface for backward compatibility
interface LegacyDynamicSelectFieldProps {
  elementInstance: FormElementInstance;
  submitValue?: (value: string) => void;
  isInvalid?: boolean;
  defaultValue?: string;
  isDisabled?: boolean;
}

// Union type for both interfaces
type DynamicSelectFieldFormElementProps<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> =
  | LegacyDynamicSelectFieldProps
  | DynamicSelectFieldReactHookFormProps<TFieldValues, TName>;

// Type guard to check if props are React Hook Form props
function isReactHookFormProps<
  TFieldValues extends FieldValues,
  TName extends FieldPath<TFieldValues>,
>(
  props: DynamicSelectFieldFormElementProps<TFieldValues, TName>,
): props is DynamicSelectFieldReactHookFormProps<TFieldValues, TName> {
  return "control" in props && "name" in props;
}

export const DynamicSelectFieldFormElement = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>(
  props: DynamicSelectFieldFormElementProps<TFieldValues, TName>,
) => {
  // If using React Hook Form integration
  if (isReactHookFormProps(props)) {
    const {
      control,
      name,
      label,
      required = false,
      placeholder,
      helperText,
      disabled = false,
      dynamicKey,
      payload = {},
      onSelectionChange,
      allowNone = false,
    } = props;

    const [isOpen, setIsOpen] = useState(false);

    // Watch the current field value to determine if we need to fetch options for initial value
    const currentValue = useWatch({
      control,
      name,
    });

    // Check if we have an initial value that requires fetching options
    const hasInitialValue = React.useMemo(() => {
      return !!currentValue;
    }, [currentValue]);

    const {
      data: options,
      isLoading,
      error,
    } = useDynamicSelect({
      key: dynamicKey,
      payload,
      enabled: isOpen || hasInitialValue, // Fetch when dropdown is opened OR when there's an initial value
    });

    return (
      <FormField
        control={control}
        name={name}
        render={({ field, fieldState }) => (
          <FormItem>
            <FormLabel
              className={cn(
                fieldState.error ? "text-red-500" : "text-muted-foreground",
              )}
            >
              {label}
              {required && <span className="text-destructive">*</span>}
            </FormLabel>
            <Select
              onValueChange={(value) => {
                field.onChange(value);
                // Find the selected option and call onSelectionChange with full object
                if (onSelectionChange) {
                  const selectedOption = options.find(
                    (option) => option.value === value,
                  );
                  onSelectionChange(selectedOption || { value, label: value });
                }
              }}
              value={field.value}
              disabled={disabled}
              onOpenChange={setIsOpen}
            >
              <FormControl>
                <SelectTrigger
                  className={cn(fieldState.error && "border-red-500")}
                >
                  <SelectValue placeholder={placeholder} />
                  {isLoading && isOpen && (
                    <Loader2 className="h-4 w-4 animate-spin ml-2" />
                  )}
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {isLoading && (
                  <div className="flex items-center justify-center py-2">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm text-muted-foreground">
                      Loading options...
                    </span>
                  </div>
                )}
                {error && (
                  <div className="flex items-center justify-center py-2">
                    <span className="text-sm text-red-500">
                      Error loading options
                    </span>
                  </div>
                )}
                {!isLoading && !error && allowNone && (
                  <SelectItem value="none">None</SelectItem>
                )}
                {!isLoading &&
                  !error &&
                  options?.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                {!isLoading && !error && options?.length === 0 && (
                  <div className="flex items-center justify-center py-2">
                    <span className="text-sm text-muted-foreground">
                      {dynamicKey === "hotels" && payload?.destination_id 
                        ? "No hotels available for the selected destination"
                        : "No options available"}
                    </span>
                  </div>
                )}
              </SelectContent>
            </Select>
            {helperText && (
              <FormDescription
                className={cn(fieldState.error && "text-red-500")}
              >
                {helperText}
              </FormDescription>
            )}
            <FormMessage />
          </FormItem>
        )}
      />
    );
  }

  // Legacy implementation for backward compatibility
  const { elementInstance, submitValue, isInvalid, defaultValue, isDisabled } =
    props as LegacyDynamicSelectFieldProps;
  const element = elementInstance as any;
  const [value, setValue] = useState(defaultValue || "");
  const [error, setError] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (defaultValue) {
      setValue(defaultValue);
    }
  }, [defaultValue]);

  useEffect(() => {
    setError(isInvalid === true);
  }, [isInvalid]);

  const {
    label,
    required,
    placeHolder,
    helperText,
    dynamicKey,
    payload = {},
  } = element.extraAttributes;

  const {
    data: options,
    isLoading,
    error: fetchError,
  } = useDynamicSelect({
    key: dynamicKey,
    payload,
    enabled: isOpen,
  });

  return (
    <FormItem className="flex w-full flex-col">
      <FormLabel
        className={cn(error ? "text-red-500" : "text-muted-foreground")}
      >
        {label}
        {required && <span className="text-destructive">*</span>}
      </FormLabel>
      <Select
        value={value}
        onValueChange={(newValue) => {
          setValue(newValue);
          if (!submitValue) return;
          const valid = validate(element, newValue);
          setError(!valid);
          if (!valid) return;
          submitValue(newValue);
        }}
        disabled={isDisabled}
        onOpenChange={setIsOpen}
      >
        <SelectTrigger className={cn("w-full", error && "border-red-500")}>
          <SelectValue placeholder={placeHolder} />
          {isLoading && isOpen && (
            <Loader2 className="h-4 w-4 animate-spin ml-2" />
          )}
        </SelectTrigger>
        <SelectContent>
          {isLoading && (
            <div className="flex items-center justify-center py-2">
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">
                Loading options...
              </span>
            </div>
          )}
          {fetchError && (
            <div className="flex items-center justify-center py-2">
              <span className="text-sm text-red-500">
                Error loading options
              </span>
            </div>
          )}
          {!isLoading &&
            !fetchError &&
            options?.map((option: { label: string; value: string }) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          {!isLoading && !fetchError && options?.length === 0 && (
            <div className="flex items-center justify-center py-2">
              <span className="text-sm text-muted-foreground">
                No options available
              </span>
            </div>
          )}
        </SelectContent>
      </Select>
      {helperText && (
        <FormDescription className={cn(error && "text-red-500")}>
          {helperText}
        </FormDescription>
      )}
      <FormMessage />
    </FormItem>
  );
};

DynamicSelectFieldFormElement.validate = validate;
