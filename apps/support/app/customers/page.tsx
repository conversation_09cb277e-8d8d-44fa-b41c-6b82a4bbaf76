"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@flinkk/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@flinkk/components/ui/dropdown-menu";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@flinkk/components/ui/avatar";
import { Badge } from "@flinkk/components/ui/badge";
import {
  PlusIcon,
  SearchIcon,
  MoreHorizontalIcon,
  ArrowUpDownIcon,
  ArrowDownIcon,
  ArrowUpIcon,
  PhoneIcon,
  MailIcon,
  BuildingIcon,
  UserIcon,
  TicketIcon,
  DollarSignIcon,
  CalendarIcon,
} from "lucide-react";

export default function CustomersPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [sortColumn, setSortColumn] = useState("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Sample customers data
  const customers = [
    {
      id: 1,
      name: "John Smith",
      email: "<EMAIL>",
      phone: "555-1234",
      company: "Acme Inc",
      status: "Active",
      tickets: 2,
      totalSpent: 2500,
      lastPurchase: "2023-06-15",
      joinDate: "2022-01-10",
    },
    {
      id: 2,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "555-2345",
      company: "XYZ Corp",
      status: "Active",
      tickets: 1,
      totalSpent: 4200,
      lastPurchase: "2023-06-20",
      joinDate: "2022-03-15",
    },
    {
      id: 3,
      name: "Michael Brown",
      email: "<EMAIL>",
      phone: "555-3456",
      company: "ABC Ltd",
      status: "Inactive",
      tickets: 0,
      totalSpent: 1800,
      lastPurchase: "2023-05-05",
      joinDate: "2022-02-20",
    },
    {
      id: 4,
      name: "Emily Davis",
      email: "<EMAIL>",
      phone: "555-4567",
      company: "123 Industries",
      status: "Active",
      tickets: 3,
      totalSpent: 3600,
      lastPurchase: "2023-06-25",
      joinDate: "2022-04-10",
    },
    {
      id: 5,
      name: "David Wilson",
      email: "<EMAIL>",
      phone: "555-5678",
      company: "Tech Solutions",
      status: "Active",
      tickets: 1,
      totalSpent: 5100,
      lastPurchase: "2023-06-18",
      joinDate: "2022-01-05",
    },
    {
      id: 6,
      name: "Jennifer Lee",
      email: "<EMAIL>",
      phone: "555-6789",
      company: "Global Services",
      status: "Inactive",
      tickets: 0,
      totalSpent: 950,
      lastPurchase: "2023-04-12",
      joinDate: "2022-05-15",
    },
    {
      id: 7,
      name: "Robert Taylor",
      email: "<EMAIL>",
      phone: "555-7890",
      company: "Innovative Systems",
      status: "Active",
      tickets: 2,
      totalSpent: 3200,
      lastPurchase: "2023-06-10",
      joinDate: "2022-03-01",
    },
  ];

  // Filter customers based on search query
  const filteredCustomers = customers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.company.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Sort customers
  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    const aValue = a[sortColumn as keyof typeof a];
    const bValue = b[sortColumn as keyof typeof b];

    if (sortDirection === "asc") {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  // Handle sort
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(column);
      setSortDirection("asc");
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active":
        return "bg-green-500";
      case "Inactive":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  // Get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase();
  };

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  // Calculate metrics
  const totalCustomers = customers.length;
  const activeCustomers = customers.filter((c) => c.status === "Active").length;
  const totalRevenue = customers.reduce(
    (sum, customer) => sum + customer.totalSpent,
    0,
  );
  const averageRevenue = totalRevenue / totalCustomers;

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Customers</h1>
        <Button>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Customer
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <UserIcon className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">Total Customers</p>
                <p className="text-2xl font-bold">{totalCustomers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <UserIcon className="h-5 w-5 text-muted-foreground" />
              <div>
                <p className="text-sm text-muted-foreground">
                  Active Customers
                </p>
                <p className="text-2xl font-bold">{activeCustomers}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(totalRevenue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <div>
                <p className="text-sm text-muted-foreground">Avg. Revenue</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(averageRevenue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Customer Management</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center mb-4">
            <div className="relative flex-1 max-w-sm">
              <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search customers..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="w-[250px] cursor-pointer"
                    onClick={() => handleSort("name")}
                  >
                    <div className="flex items-center">
                      Customer
                      {sortColumn === "name" ? (
                        sortDirection === "asc" ? (
                          <ArrowUpIcon className="ml-2 h-4 w-4" />
                        ) : (
                          <ArrowDownIcon className="ml-2 h-4 w-4" />
                        )
                      ) : (
                        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead>Contact Info</TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("company")}
                  >
                    <div className="flex items-center">
                      Company
                      {sortColumn === "company" ? (
                        sortDirection === "asc" ? (
                          <ArrowUpIcon className="ml-2 h-4 w-4" />
                        ) : (
                          <ArrowDownIcon className="ml-2 h-4 w-4" />
                        )
                      ) : (
                        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("status")}
                  >
                    <div className="flex items-center">
                      Status
                      {sortColumn === "status" ? (
                        sortDirection === "asc" ? (
                          <ArrowUpIcon className="ml-2 h-4 w-4" />
                        ) : (
                          <ArrowDownIcon className="ml-2 h-4 w-4" />
                        )
                      ) : (
                        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("tickets")}
                  >
                    <div className="flex items-center">
                      Tickets
                      {sortColumn === "tickets" ? (
                        sortDirection === "asc" ? (
                          <ArrowUpIcon className="ml-2 h-4 w-4" />
                        ) : (
                          <ArrowDownIcon className="ml-2 h-4 w-4" />
                        )
                      ) : (
                        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("totalSpent")}
                  >
                    <div className="flex items-center">
                      Total Spent
                      {sortColumn === "totalSpent" ? (
                        sortDirection === "asc" ? (
                          <ArrowUpIcon className="ml-2 h-4 w-4" />
                        ) : (
                          <ArrowDownIcon className="ml-2 h-4 w-4" />
                        )
                      ) : (
                        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer"
                    onClick={() => handleSort("lastPurchase")}
                  >
                    <div className="flex items-center">
                      Last Purchase
                      {sortColumn === "lastPurchase" ? (
                        sortDirection === "asc" ? (
                          <ArrowUpIcon className="ml-2 h-4 w-4" />
                        ) : (
                          <ArrowDownIcon className="ml-2 h-4 w-4" />
                        )
                      ) : (
                        <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                      )}
                    </div>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedCustomers.map((customer) => (
                  <TableRow key={customer.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src={`https://i.pravatar.cc/150?img=${customer.id + 20}`}
                            alt={customer.name}
                          />
                          <AvatarFallback>
                            {getInitials(customer.name)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="font-medium">{customer.name}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-1 text-xs">
                          <MailIcon className="h-3 w-3" />
                          <span>{customer.email}</span>
                        </div>
                        <div className="flex items-center gap-1 text-xs">
                          <PhoneIcon className="h-3 w-3" />
                          <span>{customer.phone}</span>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <BuildingIcon className="h-4 w-4 text-muted-foreground" />
                        <span>{customer.company}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={getStatusColor(customer.status)}
                      >
                        {customer.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <TicketIcon className="h-4 w-4 text-muted-foreground" />
                        <span>{customer.tickets}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <span>{formatCurrency(customer.totalSpent)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                        <span>{customer.lastPurchase}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontalIcon className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View profile</DropdownMenuItem>
                          <DropdownMenuItem>Edit</DropdownMenuItem>
                          <DropdownMenuItem>View tickets</DropdownMenuItem>
                          <DropdownMenuItem>View purchases</DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
                {filteredCustomers.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={8} className="h-24 text-center">
                      No customers found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
