"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@flinkk/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { Badge } from "@flinkk/components/ui/badge";
import {
  CheckCircle,
  XCircle,
  Loader2,
  Mail,
  Plus,
  Settings,
  Trash2,
  Star,
  StarOff,
  Building,
  ExternalLink,
} from "lucide-react";
import { toast } from "react-hot-toast";
import Link from "next/link";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@flinkk/components/ui/alert-dialog";

interface CRMEmailConfigListProps {
  canEdit: boolean;
}

interface EmailConfig {
  id: string;
  name: string;
  description?: string;
  fromEmail: string;
  fromName: string;
  provider: "OUTLOOK" | "GMAIL" | "FLINKK_MAIL";
  status: "ACTIVE" | "INACTIVE" | "ERROR";
  isDefault: boolean;
  lastTested?: string;
  connectedAccount?: string;
  createdAt: string;
}

export function CRMEmailConfigList({ canEdit }: CRMEmailConfigListProps) {
  const [configs, setConfigs] = useState<EmailConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [deletingConfig, setDeletingConfig] = useState<EmailConfig | null>(
    null
  );
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    loadConfigurations();
  }, []);

  const loadConfigurations = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/crm-communication/email/list");

      if (response.ok) {
        const data = await response.json();
        setConfigs(data.configs || []);
      } else {
        toast.error("Failed to load email configurations");
      }
    } catch (error) {
      console.error("Error loading email configurations:", error);
      toast.error("Failed to load email configurations");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSetDefault = async (configId: string) => {
    try {
      const response = await fetch(
        `/api/crm-communication/email/${configId}/set-default`,
        {
          method: "POST",
        }
      );

      if (response.ok) {
        toast.success("Default email configuration updated");
        loadConfigurations();
      } else {
        toast.error("Failed to set default configuration");
      }
    } catch (error) {
      toast.error("Failed to set default configuration");
    }
  };

  const handleDelete = async () => {
    if (!deletingConfig) return;

    try {
      setIsDeleting(true);
      const response = await fetch(
        `/api/crm-communication/email/${deletingConfig.id}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Email configuration deleted");
        setDeletingConfig(null);
        loadConfigurations();
      } else {
        toast.error("Failed to delete configuration");
      }
    } catch (error) {
      toast.error("Failed to delete configuration");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleConnectOAuth = (provider: string) => {
    // Store current page URL to return to after OAuth
    sessionStorage.setItem("oauth_return_url", window.location.href);

    // Redirect to OAuth flow
    const authUrl = `/api/crm-communication/email/oauth/${provider}/authorize`;
    window.location.href = authUrl;
  };

  const handleDisconnectOAuth = async (provider: string, id: string) => {
    console.log({ provider, id });

    try {
      const response = await fetch(
        `/api/crm-communication/email/oauth/${provider}/disconnect?id=${id}`,
        {
          method: "POST",
        }
      );

      if (response.ok) {
        toast.success(
          `${getProviderName(provider.toUpperCase())} account disconnected`
        );
        loadConfigurations();
      } else {
        toast.error("Failed to disconnect account");
      }
    } catch (error) {
      toast.error("Failed to disconnect account");
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return (
          <Badge variant="default" className="bg-green-500">
            <CheckCircle className="h-3 w-3 mr-1" />
            Active
          </Badge>
        );
      case "ERROR":
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Error
          </Badge>
        );
      default:
        return <Badge variant="outline">Inactive</Badge>;
    }
  };

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case "OUTLOOK":
        return <Building className="h-4 w-4" />;
      case "GMAIL":
        return <Mail className="h-4 w-4" />;
      case "FLINKK_MAIL":
        return <Settings className="h-4 w-4" />;
      default:
        return <Mail className="h-4 w-4" />;
    }
  };

  const getProviderName = (provider: string) => {
    switch (provider) {
      case "OUTLOOK":
        return "Microsoft Outlook";
      case "GMAIL":
        return "Google Gmail";
      case "FLINKK_MAIL":
        return "Flinkk Mail";
      default:
        return provider;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Add Button */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Email Configurations</h3>
          <p className="text-sm text-muted-foreground">
            Manage multiple email accounts for CRM operations
          </p>
        </div>
        {canEdit && (
          <Link href="/crm-communication-settings/email/create">
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Email Account
            </Button>
          </Link>
        )}
      </div>

      {/* Configuration Cards */}
      {configs.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Mail className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">
              No Email Configurations
            </h3>
            <p className="text-muted-foreground text-center mb-4">
              Add your first email account to start sending emails through CRM.
            </p>
            {canEdit && (
              <Link href="/crm-communication-settings/email/create">
                <Button className="flex items-center gap-2">
                  <Plus className="h-4 w-4" />
                  Add Email Account
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {configs.map((config) => (
            <Card
              key={config.id}
              className={config.isDefault ? "ring-2 ring-primary" : ""}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getProviderIcon(config.provider)}
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        {config.name}
                        {config.isDefault && (
                          <Badge
                            variant="secondary"
                            className="flex items-center gap-1"
                          >
                            <Star className="h-3 w-3" />
                            Default
                          </Badge>
                        )}
                      </CardTitle>
                      <CardDescription>
                        {config.fromEmail} • {getProviderName(config.provider)}
                        {config.connectedAccount &&
                          ` • ${config.connectedAccount}`}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(config.status)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div className="text-sm text-muted-foreground">
                    {config.description && <span>{config.description} • </span>}
                    {config.lastTested && (
                      <span>
                        Last tested:{" "}
                        {new Date(config.lastTested).toLocaleString()}
                      </span>
                    )}
                  </div>
                  {canEdit && (
                    <div className="flex items-center gap-2">
                      {/* Connect/Disconnect Account buttons for OAuth providers */}
                      {(config.provider === "OUTLOOK" ||
                        config.provider === "GMAIL") && (
                        <>
                          {!config.connectedAccount ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                handleConnectOAuth(
                                  config.provider.toLowerCase()
                                )
                              }
                              className="flex items-center gap-1 text-blue-600 hover:text-blue-700"
                            >
                              <ExternalLink className="h-3 w-3" />
                              Connect Account
                            </Button>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                handleDisconnectOAuth(
                                  config.provider.toLowerCase(),
                                  config.id
                                )
                              }
                              className="flex items-center gap-1 text-red-600 hover:text-red-700"
                            >
                              <ExternalLink className="h-3 w-3" />
                              Disconnect
                            </Button>
                          )}
                        </>
                      )}
                      {/* {!config.isDefault && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSetDefault(config.id)}
                          className="flex items-center gap-1"
                        >
                          <StarOff className="h-3 w-3" />
                          Set Default
                        </Button>
                      )} */}
                      <Link
                        href={`/crm-communication-settings/email/${config.id}/edit`}
                      >
                        <Button
                          variant="outline"
                          size="sm"
                          className="flex items-center gap-1"
                        >
                          <Settings className="h-3 w-3" />
                          Edit
                        </Button>
                      </Link>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setDeletingConfig(config)}
                        className="flex items-center gap-1 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-3 w-3" />
                        Delete
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!deletingConfig}
        onOpenChange={() => setDeletingConfig(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Email Configuration</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingConfig?.name}"? This
              action cannot be undone.
              {deletingConfig?.isDefault && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-800">
                  <strong>Warning:</strong> This is your default email
                  configuration. You may want to set another configuration as
                  default before deleting this one.
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
