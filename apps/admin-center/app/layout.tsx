import { Inter } from "next/font/google";
import { JetBrains_Mono } from "next/font/google";
// Global styles
import "@flinkk/styles/globals.css";
import { ThemeProvider } from "@flinkk/provider/theme";
import { Toaster } from "react-hot-toast";
import { siteConfig } from "config/site";
import { getServerSession } from "next-auth";
import { authOptions, CustomSession } from "@flinkk/shared-auth/options";
import { prisma } from "@flinkk/database/prisma";
import { SidebarProvider } from "@flinkk/components/ui/sidebar";
import { SidebarInset } from "@flinkk/components/ui/sidebar";
import { SessionWrapper } from "@flinkk/shared-auth/components/session-wrapper";

import { AppSidebar } from "@flinkk/shared-sidebar";
import { getSidebarData } from "config/sidebar-menu";
import { checkInventoryConfigured } from "../../sales/lib/check-inventory-configured";
import { DataDogScript } from "@flinkk/audit/datadog";
import { MicrosoftClarity } from "@flinkk/audit/microsoft-clarity";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { getCurrentUserPermissions } from "@flinkk/shared-rbac/rbac-utils";
import { filterSidebarByPermissions } from "@flinkk/shared-utils";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

const jetBrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  variable: "--font-geist-mono",
});

export const metadata = {
  title: {
    default: siteConfig.title,
    template: `${siteConfig.title} | %s`,
  },
  description: siteConfig.description,
};

export default async function RootLayout({
  children,
  sidebar,
}: Readonly<{
  children: React.ReactNode;
  sidebar: React.ReactNode;
}>) {
  const headersList = await headers();
  const pathname = headersList.get("x-next-pathname") as string;
  const session = (await getServerSession(authOptions)) as CustomSession;
  let workspaces = [];
  if (session?.userId) {
    const memberships = await prisma.memberShip.findMany({
      where: {
        userId: session?.userId,
      },
      include: {
        tenant: {
          select: {
            id: true,
            name: true,
            domain: true,
            logo: true,
          },
        },
      },
    });

    // Transform memberships to match Organization interface
    workspaces = memberships.map((membership: any) => ({
      id: membership.id,
      tenantId: membership.tenantId,
      tenant: membership.tenant,
    }));

    // Only redirect authenticated users with no workspaces
    if (workspaces.length === 0 && pathname !== "/no-organization") {
      return redirect("/no-organization");
    }
  }

  // Check if inventory is configured for current tenant
  const hasInventoryConfigured = await checkInventoryConfigured(session?.tenantId || null);

  // Get dynamic sidebar data based on inventory configuration
  const sidebarData = getSidebarData(hasInventoryConfigured);

  // Fetch user permissions and filter sidebar
  const permissions = await getCurrentUserPermissions();
  const filteredSidebarData = filterSidebarByPermissions(sidebarData, permissions);

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${jetBrainsMono.variable} font-sans antialiased`}
      >
        <SessionWrapper session={session}>
          <DataDogScript />
          <MicrosoftClarity />
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem>
            <SidebarProvider>
              <AppSidebar
                data={filteredSidebarData}
                defaultApp="Admin Center"
                user={session?.user}
                session={session}
                workspaces={workspaces}
                currentTenantId={session?.tenantId}
              />
              <SidebarInset
                className="w-full h-full overflow-y-auto"
                style={{
                  height: "calc(100vh - 16px)",
                  width: "calc(100vw - 232px)",
                }}
              >
                {children}
              </SidebarInset>
            </SidebarProvider>
            <Toaster />
          </ThemeProvider>
        </SessionWrapper>
      </body>
    </html>
  );
}
