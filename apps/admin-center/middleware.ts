import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getToken } from "next-auth/jwt";
// import { domainResolver } from "./lib/domain-resolver";

// Define public routes that don't require authentication
const publicRoutes = [
  "/sign-in",
  "/sign-up",
  "/forgot-password",
  "/reset-password",
  "/set-password",
  "/verify-email",
  "/api/auth",
  "/api/login",
  "/api/register",
  "/api/widget",
  "/api/socket",
  "/api/copilot", // Added copilot API to public routes
  "/embed.js",
  "/sdk.js", // Added SDK.js for the live chat
  "/support-chat", // Added support-chat page for public access
  "/public/whatsapp-chat", // Added support-chat page for public access
];

// Check if the route is a public route
function isPublicRoute(path: string): boolean {
  // Special case for support-chat with dynamic routes
  if (
    path.startsWith("/support-chat") ||
    path.startsWith("/public/whatsapp-chat")
  ) {
    return true;
  }
  return publicRoutes.some((route) => path.startsWith(route));
}

// Add CORS headers to the response
function addCorsHeaders(response: NextResponse) {
  response.headers.set("Access-Control-Allow-Origin", "*");
  response.headers.set(
    "Access-Control-Allow-Methods",
    "GET, POST, PUT, DELETE, OPTIONS"
  );
  response.headers.set(
    "Access-Control-Allow-Headers",
    "Content-Type, Authorization, x-copilotkit-runtime-client-gql-version"
  );
  response.headers.set("Access-Control-Max-Age", "86400"); // 24 hours
  return response;
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Handle domain resolution first (for custom domains)
  // const domainResponse = await domainResolver(request);
  // if (domainResponse) {
  //   return domainResponse;
  // }

  // Handle CORS preflight requests
  if (request.method === "OPTIONS") {
    const response = NextResponse.json({}, { status: 200 });
    return addCorsHeaders(response);
  }

  // Add CORS headers for API routes
  if (pathname.startsWith("/api/")) {
    const response = NextResponse.next();
    return addCorsHeaders(response);
  }

  // Check if it's a public route
  const isPublic = isPublicRoute(pathname);

  // Allow all public routes
  if (isPublic) {
    return NextResponse.next();
  }

  const token = await getToken({ req: request });
  const isAuthenticated = !!token;

  // If not authenticated and not a public route, redirect to sign-in
  if (!isAuthenticated && !isPublic) {
    const MAIN_APP_URL = process.env.NEXT_PUBLIC_MAIN_APP_URL as string;
    const signInUrl = new URL("/sign-in", MAIN_APP_URL);
    return NextResponse.redirect(signInUrl);
  }

  return NextResponse.next();
}

// Configure which routes use this middleware
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * 1. /api/auth/* (authentication routes)
     * 2. /_next/* (Next.js internals)
     * 3. /fonts/* (static assets)
     * 4. /images/* (static assets)
     * 5. /favicon.ico, /site.webmanifest (static assets)
     * 6. /support-chat/* (public support chat)
     */
    "/((?!_next/|fonts/|images/|favicon.ico|site.webmanifest|support-chat/).*)",
  ],
};
