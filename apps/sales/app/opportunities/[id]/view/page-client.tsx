"use client";

import React, { useState } from "react";
import toast from "react-hot-toast";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { Badge } from "@flinkk/components/ui/badge";
import {
  BoringAvatar,
  avatarPresets,
} from "@flinkk/components/ui/boring-avatar";
import { OpportunityStatus } from "@/features/crm/components/opportunity-status";
import {
  ArrowLeftIcon,
  PhoneIcon,
  MailIcon,
  CalendarIcon,
  UserIcon,
  BuildingIcon,
  DollarSignIcon,
} from "lucide-react";
import { FlinkkClient } from "@flinkk/services";
import { QuoteGeneratorButton } from "@flinkk/quote-pdf";
import { QuotePayload } from "@flinkk/quote-pdf/types";

// Import StatusOptionClient interface from shared actions
import type { StatusOptionClient } from "@/lib/actions/status-options";

interface OpportunityDetailProps {
  id?: string;
  opportunity: any;
  statusOptions: StatusOptionClient[];
}

export function OpportunityDetail({
  id,
  opportunity: initialOpportunity,
  statusOptions,
}: OpportunityDetailProps) {
  const [opportunity, setOpportunity] = useState(initialOpportunity);
  const [isCallDialogOpen, setIsCallDialogOpen] = useState(false);
  const [isEmailDialogOpen, setIsEmailDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!opportunity || !id) return;

    try {
      // Use FlinkkClient.update instead of server action
      await FlinkkClient.update("opportunity", id, {
        stage: newStatus,
      });

      // Update the local state immediately to reflect the change
      setOpportunity((prev: any) => ({
        ...prev,
        stage: newStatus,
      }));

      toast.success("Opportunity status updated successfully");
    } catch (error) {
      console.error("Error updating opportunity status:", error);
      toast.error("Failed to update opportunity status. Please try again.");
    }
  };

  // Generate quote data from opportunity
  const generateQuoteData = (): QuotePayload => {
    if (!opportunity) {
      throw new Error("No opportunity data available");
    }

    const today = new Date();
    const validUntil = new Date();
    validUntil.setDate(today.getDate() + 30); // Valid for 30 days

    return {
      quote_number: `Q-${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, "0")}${today.getDate().toString().padStart(2, "0")}-${opportunity.id.slice(-4)}`,
      date_issued: today.toISOString().split("T")[0],
      valid_until: validUntil.toISOString().split("T")[0],
      customer_name:
        opportunity.account?.name || opportunity.contact?.name || "Customer",
      customer_address:
        opportunity.account?.billing_address || "Address not available",
      payment_terms: "Net 30",
      line_items: [
        {
          description: opportunity.name || "Opportunity Services",
          quantity: 1,
          unit_price: opportunity.value || 0,
        },
      ],
    };
  };

  return (
    <div className="space-y-2" id={id}>
      {/* Header with back button and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Button variant="ghost" size="icon" className="mr-2">
            <ArrowLeftIcon className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">
            Opportunity Details
          </h1>
        </div>
        <div className="flex space-x-2">
          {opportunity && (
            <QuoteGeneratorButton
              quote={generateQuoteData()}
              variant="download"
              size="sm"
              options={{
                company_name: "Flinkk CRM",
                company_address: "Your Company Address",
              }}
            >
              Generate Quote
            </QuoteGeneratorButton>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsEditDialogOpen(true)}
          >
            Edit
          </Button>
          <Button variant="outline" size="sm">
            Mark as Won
          </Button>
          <Button variant="outline" size="sm">
            Mark as Lost
          </Button>
        </div>
      </div>

      {/* Opportunity status */}
      {opportunity && (
        <OpportunityStatus
          status={opportunity.stage}
          onStatusChange={handleStatusChange}
          statusOptions={statusOptions}
        />
      )}

      {/* Opportunity overview */}
      {opportunity ? (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-xl">{opportunity.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-2">
              <div className="md:w-1/3 flex flex-col items-center">
                <BoringAvatar
                  name={
                    opportunity.contact?.email ||
                    `${opportunity.contact?.firstName} ${opportunity.contact?.lastName}` ||
                    opportunity.name ||
                    `opportunity-${opportunity.id}`
                  }
                  size="3xl"
                  {...avatarPresets.opportunity}
                  className="mb-4"
                />
                <h2 className="text-xl font-semibold text-center">
                  {opportunity.contact
                    ? `${opportunity.contact.firstName} ${opportunity.contact.lastName}`
                    : "No Contact"}
                </h2>
                <p className="text-muted-foreground text-center mb-4">
                  {opportunity.account?.name || "No Company"}
                </p>
                <div className="flex space-x-2 mb-4">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setIsCallDialogOpen(true)}
                  >
                    <PhoneIcon className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setIsEmailDialogOpen(true)}
                  >
                    <MailIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="md:w-2/3 grid grid-cols-1 md:grid-cols-2 gap-2">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Value</p>
                  <div className="flex items-center">
                    <p>
                      {opportunity.value
                        ? opportunity.value.toLocaleString()
                        : "0"}
                    </p>
                  </div>
                </div>

                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">
                    Expected Close Date
                  </p>
                  <div className="flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                    <p>
                      {opportunity.expectedCloseDate
                        ? formatDate(opportunity.expectedCloseDate)
                        : "Not set"}
                    </p>
                  </div>
                </div>

                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Company</p>
                  <div className="flex items-center">
                    <BuildingIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                    <p>{opportunity.account?.name || "No Company"}</p>
                  </div>
                </div>

                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Contact</p>
                  <div className="flex items-center">
                    <UserIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                    <p>{opportunity.contact?.name || "No Contact"}</p>
                  </div>
                </div>

                {opportunity.priority && (
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Priority</p>
                    <div className="flex items-center">
                      <Badge
                        variant="outline"
                        className={
                          opportunity.priority === "High"
                            ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                            : opportunity.priority === "Medium"
                              ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                              : "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                        }
                      >
                        {opportunity.priority}
                      </Badge>
                    </div>
                  </div>
                )}

                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">
                    Win Probability
                  </p>
                  <div className="flex items-center">
                    <p>{opportunity.probability || 0}%</p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : null}
    </div>
  );
}
