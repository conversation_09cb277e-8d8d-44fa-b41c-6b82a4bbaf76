"use client";

import { useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { RoomBlock, PricingData } from "../_components/hotel-booking-calendar";
import { toast } from "sonner";

interface SaveRoomBookingsParams {
  roomBlocks: RoomBlock[];
  cartId: string;
  hotelId?: string;
  hotelName?: string;
  pricingData?: PricingData | null;
  // Additional fields for cart metadata sync
  destinationId?: string;
  destinationName?: string;
  checkInDate?: string;
  checkOutDate?: string;
}

interface SaveRoomBookingsResult {
  success: boolean;
  savedCount: number;
  errors: string[];
}

export function useSaveRoomBookings() {
  const [isLoading, setIsLoading] = useState(false);

  const mutation = useMutation({
    mutationFn: async ({
      roomBlocks,
      cartId,
      hotelId,
      hotelName,
      pricingData,
      destinationId,
      destinationName,
      checkInDate,
      checkOutDate,
    }: SaveRoomBookingsParams): Promise<SaveRoomBookingsResult> => {
      try {
        // Map all room blocks to cart items
        const items = roomBlocks.map((block) => {
          // Find occupancy type and meal plan display names from pricing data
          const occupancyType = pricingData?.pricing?.occupancy_configs?.find(
            (config) => config.id === block.occupancy_type_id
          );
          const mealPlan = pricingData?.pricing?.meal_plans?.find(
            (plan) => plan.id === block.meal_plan_id
          );

          return {
            variant_id: block.room_id, // Use room_id as variant_id
            quantity: 1, // Each booking is 1 unit
            title: block.room_name, // Use room name for user-friendly display
            unit_price: block.total, // Total price for the entire booking
            // Product-related fields at top level
            product_collection: "hotel-rooms",
            product_description: `${block.room_name} - ${block.config_name}`,
            product_handle: block.room_id.toLowerCase().replace(/[^a-z0-9]/g, '-'),
            product_id: block.product_id,
            product_subtitle: `${block.nights} night${block.nights !== 1 ? 's' : ''}`,
            product_title: block.room_name,
            product_type: "hotel-room",
            product_type_id: block.product_id,
            raw_compare_at_unit_price: block.rate,
            metadata: {
              room_id: block.room_id,
              hotel_id: hotelId || "unknown",
              hotel_name: hotelName || "Unknown Hotel",
              start_date: new Date(block.check_in).toISOString(),
              end_date: new Date(block.check_out).toISOString(),
              room_config_id: block.product_id,
              room_config_name: block.config_name,
              number_of_rooms: 1,
              // Add occupancy type and meal plan metadata
              ...(block.occupancy_type_id && {
                occupancy_type_id: block.occupancy_type_id,
                occupancy_type_name: occupancyType?.title || occupancyType?.name || "Unknown Occupancy",
              }),
              ...(block.meal_plan_id && {
                meal_plan_id: block.meal_plan_id,
                meal_plan_name: mealPlan?.title || mealPlan?.name || "Unknown Meal Plan",
              }),
              // Add destination information if available
              ...(destinationId && { destination_id: destinationId }),
              ...(destinationName && { destination_name: destinationName }),
            },
          };
        });

        console.log("🛒 Calling wrapper API to save room bookings:", {
          cartId,
          items,
          destinationId,
          destinationName,
          hotelId,
          hotelName,
          checkInDate,
          checkOutDate,
        });

        // Call the wrapper API
        const response = await fetch("/api/room-bookings/save", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            cartId,
            items,
            destination_id: destinationId,
            destination_name: destinationName,
            hotel_id: hotelId,
            hotel_name: hotelName,
            check_in_date: checkInDate,
            check_out_date: checkOutDate,
          }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        const result = await response.json();
        console.log("🎉 Wrapper API call successful:", result);

        // Log cart metadata update status
        if (result.cartMetadataUpdated) {
          console.log("✅ Cart metadata updated successfully");
        } else if (result.cartMetadataError) {
          console.warn("⚠️ Cart metadata update failed:", result.cartMetadataError);
        }

        return {
          success: true,
          savedCount: roomBlocks.length,
          errors: [],
        };
      } catch (error) {
        console.error("Failed to save room bookings via wrapper API:", error);
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";
        return {
          success: false,
          savedCount: 0,
          errors: [`Failed to save room bookings: ${errorMessage}`],
        };
      }
    },
    onSuccess: (result) => {
      if (result.success) {
        toast.success(
          `✅ ${result.savedCount} room booking${result.savedCount !== 1 ? "s" : ""} added to quotation`,
        );
      } else {
        toast.error(`❌ Failed to save room bookings`);
        result.errors.forEach((error) => {
          toast.error(error);
        });
      }
    },
    onError: (error) => {
      console.error("Error saving room bookings:", error);
      toast.error("❌ Failed to save room bookings");
    },
  });

  const saveRoomBookings = async (params: SaveRoomBookingsParams) => {
    console.log("🏨 saveRoomBookings called with params:", params);
    setIsLoading(true);
    try {
      const result = await mutation.mutateAsync(params);
      console.log("✅ saveRoomBookings completed:", result);
      return result;
    } catch (error) {
      console.error("❌ saveRoomBookings failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    saveRoomBookings,
    isLoading: isLoading || mutation.isPending,
    error: mutation.error,
  };
}
