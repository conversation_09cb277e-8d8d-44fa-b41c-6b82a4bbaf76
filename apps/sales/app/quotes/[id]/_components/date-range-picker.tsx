"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@flinkk/components/ui/card";
import { DatePicker } from "@flinkk/components/ui/date-picker";
import { Calendar, CheckCircle } from "lucide-react";
import { format, isAfter, isBefore, addDays } from "date-fns";
import { DateRange } from "./hotel-booking-calendar";

interface DateRangePickerProps {
  onDateRangeChange: (dateRange: DateRange) => void;
  initialDateRange?: DateRange;
  unavailableDates?: Date[];
  className?: string;
}

export function DateRangePicker({
  onDateRangeChange,
  initialDateRange,
  unavailableDates = [],
  className = "",
}: DateRangePickerProps) {
  const [checkIn, setCheckIn] = useState<Date | null>(
    initialDateRange?.checkIn || null
  );
  const [checkOut, setCheckOut] = useState<Date | null>(
    initialDateRange?.checkOut || null
  );
  const [errors, setErrors] = useState<{
    checkIn?: string;
    checkOut?: string;
  }>({});

  const validateDates = (newCheckIn: Date | null, newCheckOut: Date | null) => {
    const newErrors: { checkIn?: string; checkOut?: string } = {};

    if (newCheckIn && newCheckOut) {
      // Check if check-out is after check-in
      if (!isAfter(newCheckOut, newCheckIn)) {
        newErrors.checkOut = "Check-out date must be after check-in date";
      }

      // Check if dates are in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (isBefore(newCheckIn, today)) {
        newErrors.checkIn = "Check-in date cannot be in the past";
      }

      // Check for unavailable dates in the range
      if (unavailableDates.length > 0) {
        const dateRange = [];
        let currentDate = new Date(newCheckIn);
        while (currentDate <= newCheckOut) {
          dateRange.push(new Date(currentDate));
          currentDate = addDays(currentDate, 1);
        }

        const hasUnavailableDate = dateRange.some(date =>
          unavailableDates.some(unavailableDate =>
            date.toDateString() === unavailableDate.toDateString()
          )
        );

        if (hasUnavailableDate) {
          newErrors.checkIn = "Selected date range contains unavailable dates";
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCheckInChange = (date: Date | string) => {
    const newCheckIn = date instanceof Date ? date : new Date(date);
    setCheckIn(newCheckIn);

    // Auto-adjust check-out if it's before or same as new check-in
    let newCheckOut = checkOut;
    if (checkOut && !isAfter(checkOut, newCheckIn)) {
      newCheckOut = addDays(newCheckIn, 1);
      setCheckOut(newCheckOut);
    }

    const isValid = validateDates(newCheckIn, newCheckOut);
    if (isValid) {
      onDateRangeChange({ checkIn: newCheckIn, checkOut: newCheckOut });
    }
  };

  const handleCheckOutChange = (date: Date | string) => {
    const newCheckOut = date instanceof Date ? date : new Date(date);
    setCheckOut(newCheckOut);

    const isValid = validateDates(checkIn, newCheckOut);
    if (isValid) {
      onDateRangeChange({ checkIn, checkOut: newCheckOut });
    }
  };

  const isDateRangeComplete = checkIn && checkOut && Object.keys(errors).length === 0;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <Calendar className="h-5 w-5" />
          Select Check-in & Check-out Dates
          {isDateRangeComplete && (
            <CheckCircle className="h-5 w-5 text-green-600" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Check-in Date */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Check-in Date
              <span className="text-destructive">*</span>
            </label>
            <DatePicker
              value={checkIn}
              onChange={handleCheckInChange}
              placeholder="Select check-in date"
              className="w-full"
            />
            {errors.checkIn && (
              <p className="text-xs text-red-600">{errors.checkIn}</p>
            )}
          </div>

          {/* Check-out Date */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Check-out Date
              <span className="text-destructive">*</span>
            </label>
            <DatePicker
              value={checkOut}
              onChange={handleCheckOutChange}
              placeholder="Select check-out date"
              className="w-full"
              disabled={!checkIn}
            />
            {errors.checkOut && (
              <p className="text-xs text-red-600">{errors.checkOut}</p>
            )}
          </div>
        </div>

        {/* Date Range Summary */}
        {isDateRangeComplete && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-green-800">
              <CheckCircle className="h-4 w-4" />
              <span className="font-medium">
                {format(checkIn!, "MMM dd, yyyy")} - {format(checkOut!, "MMM dd, yyyy")}
              </span>
              <span className="text-green-600">
                ({Math.ceil((checkOut!.getTime() - checkIn!.getTime()) / (1000 * 60 * 60 * 24))} nights)
              </span>
            </div>
          </div>
        )}

        {/* Helper Text */}
        <div className="mt-4 text-xs text-muted-foreground">
          <p>• Check-out date must be after check-in date</p>
          <p>• Dates cannot be in the past</p>
          {unavailableDates.length > 0 && (
            <p>• Some dates may be unavailable based on hotel availability</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
