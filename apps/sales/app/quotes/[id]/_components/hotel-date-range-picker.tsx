"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@flinkk/components/ui/card";
import { DateFieldFormElement } from "@flinkk/dynamic-form/form-elements";
import { Control } from "react-hook-form";
import { Calendar, CalendarDays } from "lucide-react";

interface HotelDateRangePickerProps {
  control: Control<any>;
  className?: string;
}

export function HotelDateRangePicker({ control, className = "" }: HotelDateRangePickerProps) {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <CalendarDays className="h-5 w-5" />
          Check-in & Check-out Dates
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <DateFieldFormElement
            name="checkInDate"
            label="Check-in Date"
            control={control}
            required={true}
            helperText="Select your arrival date"
          />
          <DateFieldFormElement
            name="checkOutDate"
            label="Check-out Date"
            control={control}
            required={true}
            helperText="Select your departure date"
          />
        </div>
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-blue-600" />
            <p className="text-sm text-blue-700">
              Room availability will be shown based on your selected dates
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 