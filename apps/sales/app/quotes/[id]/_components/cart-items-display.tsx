"use client";

import React from "react";
import { Button } from "@flinkk/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@flinkk/components/ui/card";
import { Badge } from "@flinkk/components/ui/badge";
import { Bed, Calendar, DollarSign, Users, Utensils, Hotel } from "lucide-react";
import { format } from "date-fns";

interface CartItem {
  id: string;
  variant_id: string;
  title: string;
  quantity: number;
  unit_price: number;
  product_id?: string;
  product_title?: string;
  product_description?: string;
  product_subtitle?: string;
  product_type?: string;
  product_type_id?: string;
  product_collection?: string;
  product_handle?: string;
  raw_compare_at_unit_price?: number;
  metadata?: {
    room_id?: string;
    hotel_id?: string;
    hotel_name?: string;
    start_date?: string;
    end_date?: string;
    room_config_id?: string;
    room_config_name?: string;
    number_of_rooms?: number;
    occupancy_type_id?: string;
    occupancy_type_name?: string;
    meal_plan_id?: string;
    meal_plan_name?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

interface CartDetails {
  id: string;
  email?: string;
  total: number;
  subtotal?: number;
  tax_total?: number;
  items: CartItem[];
  [key: string]: any;
}

interface SavedRoomBookingsProps {
  cartDetails: CartDetails | null;
  formatCurrency?: (amount: number) => string;
  currency?: string;
  className?: string;
}

export function SavedRoomBookings({
  cartDetails,
  formatCurrency,
  currency = "GBP",
  className = ""
}: SavedRoomBookingsProps) {
  // Don't render anything if no cart details
  if (!cartDetails) {
    return null;
  }

  // Filter only hotel room items from cart
  const hotelItems = cartDetails.items?.filter(item =>
    item.metadata?.hotel_name || item.metadata?.room_config_name
  ) || [];

  // Don't render if no hotel items
  if (hotelItems.length === 0) {
    return null;
  }

  // Calculate totals for hotel items only
  const totalNights = hotelItems.reduce((sum, item) => {
    if (item.metadata?.start_date && item.metadata?.end_date) {
      const checkIn = new Date(item.metadata.start_date);
      const checkOut = new Date(item.metadata.end_date);
      const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
      return sum + nights;
    }
    return sum + (item.quantity || 1);
  }, 0);

  const totalAmount = hotelItems.reduce((sum, item) => sum + (item.unit_price * item.quantity), 0);

  const defaultFormatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-GB", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  const currencyFormatter = formatCurrency || defaultFormatCurrency;

  const formatDate = (dateStr: string) => {
    try {
      return format(new Date(dateStr), "MMM d, yyyy");
    } catch {
      return dateStr;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bed className="h-5 w-5" />
            Saved Room Bookings
          </CardTitle>
          <Badge variant="secondary">
            {hotelItems.length} room{hotelItems.length !== 1 ? "s" : ""} saved
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Hotel Items List */}
        <div className="space-y-3">
          {hotelItems.map((item, index) => (
            <SavedRoomCard
              key={item.id || index}
              item={item}
              formatCurrency={currencyFormatter}
              formatDate={formatDate}
            />
          ))}
        </div>

        {/* Summary Totals */}
        <div className="border-t pt-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Total Nights:</span>
              <span className="font-medium">{totalNights}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Total Rooms:</span>
              <span className="font-medium">{hotelItems.length}</span>
            </div>
            <div className="flex justify-between text-lg font-semibold border-t pt-2">
              <span>Total Amount:</span>
              <span className="text-green-600">
                {currencyFormatter(totalAmount)}
              </span>
            </div>
          </div>
        </div>

        {/* Booking Details */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• These bookings have been saved to your cart</p>
          <p>• Room rates are confirmed and locked in</p>
          <p>• Changes may require rebooking</p>
        </div>
      </CardContent>
    </Card>
  );
}

interface SavedRoomCardProps {
  item: CartItem;
  formatCurrency: (amount: number) => string;
  formatDate: (dateStr: string) => string;
}

function SavedRoomCard({ item, formatCurrency, formatDate }: SavedRoomCardProps) {
  const metadata = item.metadata;
  const roomName = item.product_title || metadata?.room_config_name || 'Unknown Room';
  const configName = metadata?.room_config_name || item.product_subtitle || 'Standard';

  // Calculate nights if dates are available
  let nights = item.quantity || 1;
  if (metadata?.start_date && metadata?.end_date) {
    const checkIn = new Date(metadata.start_date);
    const checkOut = new Date(metadata.end_date);
    nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
  }

  const totalPrice = item.unit_price * item.quantity;
  const ratePerNight = nights > 0 ? totalPrice / nights : item.unit_price;

  return (
    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-gray-50">
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-2">
          <Bed className="h-4 w-4 text-gray-500" />
          <span className="font-medium text-sm">{roomName}</span>
          <Badge variant="outline" className="text-xs">
            {configName}
          </Badge>
        </div>

        <div className="flex items-center gap-4 text-sm text-gray-600">
          {metadata?.start_date && metadata?.end_date ? (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>
                {formatDate(metadata.start_date)} – {formatDate(metadata.end_date)}
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Booking confirmed</span>
            </div>
          )}
          <div className="flex items-center gap-1">
            <span>
              ({nights} night{nights !== 1 ? "s" : ""})
            </span>
          </div>
        </div>

        <div className="flex items-center gap-2 mt-2">
          <span className="text-sm text-green-600 font-medium">
            {formatCurrency(ratePerNight)}/night × {nights} = {formatCurrency(totalPrice)}
          </span>
        </div>

        {/* Additional metadata display */}
        {metadata && (
          <div className="flex items-center gap-4 mt-2 text-xs text-gray-500">
            {metadata.hotel_name && (
              <div className="flex items-center gap-1">
                <Hotel className="h-3 w-3" />
                <span>{metadata.hotel_name}</span>
              </div>
            )}
            {metadata.occupancy_type_name && (
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                <span>{metadata.occupancy_type_name}</span>
              </div>
            )}
            {metadata.meal_plan_name && (
              <div className="flex items-center gap-1">
                <Utensils className="h-3 w-3" />
                <span>{metadata.meal_plan_name}</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// Legacy export for backward compatibility
export function CartItemsDisplay(props: SavedRoomBookingsProps) {
  return <SavedRoomBookings {...props} />;
}
