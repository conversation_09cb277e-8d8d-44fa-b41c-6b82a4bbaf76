"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@flinkk/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import { ChevronLeft, ChevronRight, Loader2Icon } from "lucide-react";
import {
  format,
  addMonths,
  subMonths,
  startOfMonth,
  endOfMonth,
  getYear,
  setYear,
  setMonth,
} from "date-fns";
import {
  RoomAvailabilityGrid,
  validateGuestCapacityForHotel,
} from "./room-availability-grid";
import { RoomSelectionSummary } from "./room-selection-summary";
import { MealPlan, OccupancyConfig } from "@flinkk/inventory-api";

// Export these interfaces for use in other components
export interface DateRange {
  checkIn: Date | null;
  checkOut: Date | null;
}

export interface GuestDetails {
  adults: number;
  children: number;
}

// Types
export interface RoomConfiguration {
  id: string;
  title: string;
  description?: string;
  capacity?: number;
  basePrice?: number;
}

export interface Room {
  id: string;
  room_id: string;
  product_id: string;
  room_name: string;
  room_number?: string;
}

export interface RoomBlock {
  id: string;
  room_id: string;
  room_name: string;
  product_id: string;
  config_name: string;
  check_in: string;
  check_out: string;
  nights: number;
  rate: number;
  total: number;
  occupancy_type_id?: string;
  meal_plan_id?: string;
}

export interface AvailabilityRecord {
  room_id: string;
  room_number: string;
  room_name: string;
  config_name: string;
  from_date: string;
  to_date: string;
  status: string;
  quantity: number;
  dynamic_price?: number | null;
  notes?: string;
  order_id?: string;
}

export interface AvailabilityData {
  room_configs: RoomConfiguration[];
  rooms: Room[];
  availability: AvailabilityRecord[];
}

export interface BasePriceRule {
  id: string;
  amount: string;
  currency_code: string;
  description?: string;
  hotel_id: string;
  room_config_id: string;
  occupancy_type_id: string;
  meal_plan_id: string;
  min_occupancy: number;
  max_occupancy?: number;
  default_gross_cost: string;
  default_fixed_margin?: string;
  default_margin_percentage: string;
  default_total: string;
  monday_price: string;
  tuesday_price: string;
  wednesday_price: string;
  thursday_price: string;
  friday_price: string;
  saturday_price: string;
  sunday_price: string;
  monday_gross_cost: string;
  monday_fixed_margin: string;
  monday_margin_percentage: string;
  tuesday_gross_cost: string;
  tuesday_fixed_margin: string;
  tuesday_margin_percentage: string;
  wednesday_gross_cost: string;
  wednesday_fixed_margin: string;
  wednesday_margin_percentage: string;
  thursday_gross_cost: string;
  thursday_fixed_margin: string;
  thursday_margin_percentage: string;
  friday_gross_cost: string;
  friday_fixed_margin: string;
  friday_margin_percentage: string;
  saturday_gross_cost: string;
  saturday_fixed_margin: string;
  saturday_margin_percentage: string;
  sunday_gross_cost: string;
  sunday_fixed_margin: string;
  sunday_margin_percentage: string;
}

export interface SeasonalPriceRule {
  id: string;
  start_date: string;
  end_date: string;
  amount: string;
  currency_code: string;
  name?: string;
  description?: string;
  priority: number;
  base_price_rule_id: string;
  min_nights?: number;
  max_nights?: number;
  day_of_week_constraints?: string;
}

export interface PricingData {
  success: boolean;
  hotel: {
    id: string;
    name: string;
    handle: string;
  };
  pricing: {
    base_price_rules: BasePriceRule[];
    seasonal_price_rules: SeasonalPriceRule[];
    meal_plans: MealPlan[];
    occupancy_configs: OccupancyConfig[];
    currency_code: string;
    store_context: boolean;
  };
}

export interface ResolvedPrice {
  price: number;
  type: "base" | "seasonal";
  rule_name: string;
}

interface HotelBookingCalendarProps {
  selectedHotel?: string;
  dateRange?: DateRange;
  guestDetails?: GuestDetails;
  initialRoomBlocks?: RoomBlock[];
  onRoomBlocksChange?: (roomBlocks: RoomBlock[]) => void;
  onAddToQuotation?: (roomBlocks: RoomBlock[], hotelData?: any) => void;
  isLoadingSave?: boolean;
  className?: string;
}

export function HotelBookingCalendar({
  selectedHotel,
  dateRange,
  guestDetails,
  initialRoomBlocks = [],
  onRoomBlocksChange,
  onAddToQuotation,
  isLoadingSave = false,
  className = "",
}: HotelBookingCalendarProps) {
  // State
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedConfig, setSelectedConfig] = useState<string>("all");
  const [availabilityData, setAvailabilityData] =
    useState<AvailabilityData | null>(null);
  const [pricingData, setPricingData] = useState<PricingData | null>(null);
  const [roomBlocks, setRoomBlocks] = useState<RoomBlock[]>(initialRoomBlocks);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generate month options (January to December)
  const monthOptions = [
    { value: "0", label: "January" },
    { value: "1", label: "February" },
    { value: "2", label: "March" },
    { value: "3", label: "April" },
    { value: "4", label: "May" },
    { value: "5", label: "June" },
    { value: "6", label: "July" },
    { value: "7", label: "August" },
    { value: "8", label: "September" },
    { value: "9", label: "October" },
    { value: "10", label: "November" },
    { value: "11", label: "December" },
  ];

  // Generate year options (current year - 2 to current year + 5)
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 10 }, (_, i) => {
    const year = currentYear - 2 + i;
    return { value: year.toString(), label: year.toString() };
  });

  // Sync with initial room blocks when they change
  useEffect(() => {
    if (initialRoomBlocks.length > 0) {
      console.log("🏨 Syncing with initial room blocks:", initialRoomBlocks);
      setRoomBlocks(initialRoomBlocks);
    }
  }, [initialRoomBlocks]);

  // Fetch availability and pricing data when hotel, month, or guest details change
  useEffect(() => {
    if (selectedHotel) {
      fetchHotelData();
    } else {
      setAvailabilityData(null);
      setPricingData(null);
      setRoomBlocks([]);
    }
  }, [selectedHotel, currentMonth, guestDetails]);

  // Notify parent of room blocks changes
  useEffect(() => {
    if (onRoomBlocksChange) {
      onRoomBlocksChange(roomBlocks);
    }
  }, [roomBlocks, onRoomBlocksChange]);

  const fetchHotelData = async () => {
    if (!selectedHotel) return;

    setLoading(true);
    setError(null);

    try {
      const startDate = format(startOfMonth(currentMonth), "yyyy-MM-dd");
      const endDate = format(endOfMonth(currentMonth), "yyyy-MM-dd");

      // Build query parameters for guest-based requests
      const availabilityParams = new URLSearchParams({
        hotel_id: selectedHotel,
        start_date: startDate,
        end_date: endDate,
        consolidate: "true",
      });

      const pricingParams = new URLSearchParams({
        hotel_id: selectedHotel,
        currency: "GBP",
        start_date: startDate,
        end_date: endDate,
      });

      // Add guest details if available
      if (guestDetails) {
        availabilityParams.append("adults", guestDetails.adults.toString());
        availabilityParams.append("children", guestDetails.children.toString());
        pricingParams.append("adults", guestDetails.adults.toString());
        pricingParams.append("children", guestDetails.children.toString());
      }

      // Fetch both availability and pricing data concurrently
      const [availabilityResponse, pricingResponse] = await Promise.all([
        fetch(
          `/api/hotel-management/availability?${availabilityParams.toString()}`
        ),
        fetch(`/api/hotel-management/pricing?${pricingParams.toString()}`),
      ]);

      if (!availabilityResponse.ok) {
        const errorData = await availabilityResponse.json();
        throw new Error(errorData.error || "Failed to fetch availability data");
      }

      if (!pricingResponse.ok) {
        const errorData = await pricingResponse.json();
        throw new Error(errorData.error || "Failed to fetch pricing data");
      }

      const [availabilityData, pricingData] = await Promise.all([
        availabilityResponse.json(),
        pricingResponse.json(),
      ]);

      setAvailabilityData(availabilityData);
      setPricingData(pricingData);
    } catch (err: any) {
      console.error("Error fetching hotel data:", err);
      setError(err.message || "Failed to load hotel data");
    } finally {
      setLoading(false);
    }
  };

  const handlePreviousMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const handleMonthChange = (monthValue: string) => {
    const monthIndex = parseInt(monthValue);
    setCurrentMonth(setMonth(currentMonth, monthIndex));
  };

  const handleYearChange = (yearValue: string) => {
    const year = parseInt(yearValue);
    setCurrentMonth(setYear(currentMonth, year));
  };

  const handleRoomBlockAdd = (roomBlock: RoomBlock) => {
    setRoomBlocks((prev) => [...prev, roomBlock]);
  };

  const handleRoomBlockRemove = (blockId: string) => {
    setRoomBlocks((prev) => prev.filter((block) => block.id !== blockId));
  };

  const handleRoomBlockUpdate = (
    blockId: string,
    updates: Partial<RoomBlock>
  ) => {
    setRoomBlocks((prev) =>
      prev.map((block) =>
        block.id === blockId ? { ...block, ...updates } : block
      )
    );
  };

  // Filter rooms by selected configuration
  const filteredRooms =
    availabilityData?.rooms.filter(
      (room) => selectedConfig === "all" || room.product_id === selectedConfig
    ) || [];

  if (!selectedHotel) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Please select a hotel to view room availability
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Month Navigation and Filters */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handlePreviousMonth}
                disabled={loading}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {/* Month Dropdown */}
              <Select
                value={currentMonth.getMonth().toString()}
                onValueChange={handleMonthChange}
              >
                <SelectTrigger className="w-[70px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {monthOptions.map((month) => (
                    <SelectItem key={month.value} value={month.value}>
                      {month.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Year Dropdown */}
              <Select
                value={getYear(currentMonth).toString()}
                onValueChange={handleYearChange}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {yearOptions.map((year) => (
                    <SelectItem key={year.value} value={year.value}>
                      {year.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleNextMonth}
                disabled={loading}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center gap-4">
              {/* Room Type Filter */}
              {availabilityData?.room_configs && (
                <Select
                  value={selectedConfig}
                  onValueChange={setSelectedConfig}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Filter by room type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Room Types</SelectItem>
                    {availabilityData.room_configs.map((config) => (
                      <SelectItem key={config.id} value={config.id}>
                        {config.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading && (
            <div className="flex items-center justify-center py-8">
              <Loader2Icon className="h-6 w-6 animate-spin mr-2" />
              <span>Loading availability...</span>
            </div>
          )}

          {error && (
            <div className="text-center py-8 text-red-600">
              <p>{error}</p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={fetchHotelData}
                className="mt-2"
              >
                Retry
              </Button>
            </div>
          )}

          {availabilityData && !loading && !error && (
            <RoomAvailabilityGrid
              availabilityData={availabilityData}
              pricingData={pricingData}
              filteredRooms={filteredRooms}
              currentMonth={currentMonth}
              roomBlocks={roomBlocks}
              onRoomBlockAdd={handleRoomBlockAdd}
              guestDetails={guestDetails}
            />
          )}
        </CardContent>
      </Card>

      {/* Room Selection Summary */}
      {roomBlocks.length > 0 && (
        <RoomSelectionSummary
          roomBlocks={roomBlocks}
          onRemoveBlock={handleRoomBlockRemove}
          onUpdateBlock={handleRoomBlockUpdate}
          roomConfigurations={availabilityData?.room_configs || []}
          pricingData={pricingData}
          currency={pricingData?.pricing?.currency_code || "GBP"}
          onAddToQuotation={
            onAddToQuotation
              ? () =>
                  onAddToQuotation(roomBlocks, {
                    selectedHotel,
                    availabilityData,
                    pricingData,
                  })
              : undefined
          }
          isLoading={isLoadingSave}
        />
      )}
    </div>
  );
}
