"use client";

import React from "react";
import { Button } from "@flinkk/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { Badge } from "@flinkk/components/ui/badge";
import { X, Bed, Calendar, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { RoomBlock, RoomConfiguration, PricingData } from "./hotel-booking-calendar";

interface RoomSelectionSummaryProps {
  roomBlocks: RoomBlock[];
  onRemoveBlock: (blockId: string) => void;
  roomConfigurations: RoomConfiguration[];
  pricingData?: PricingData | null;
  currency?: string;
  onAddToQuotation?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function RoomSelectionSummary({
  roomBlocks,
  onRemoveBlock,
  roomConfigurations: _roomConfigurations,
  pricingData: _pricingData,
  currency = "GBP",
  onAddToQuotation,
  isLoading = false,
  className = "",
}: RoomSelectionSummaryProps) {
  // Calculate totals
  const totalNights = roomBlocks.reduce((sum, block) => sum + block.nights, 0);
  const totalAmount = roomBlocks.reduce((sum, block) => sum + block.total, 0);

  const formatDate = (dateStr: string) => {
    return format(new Date(dateStr), "MMM d, yyyy");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-GB", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };







  if (roomBlocks.length === 0) {
    return null;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bed className="h-5 w-5" />
            Selected Room Bookings
          </CardTitle>
          <Badge variant="secondary">
            {roomBlocks.length} room{roomBlocks.length !== 1 ? "s" : ""}{" "}
            selected
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Room Blocks List */}
        <div className="space-y-3">
          {roomBlocks.map((block) => (
            <div
              key={block.id}
              className="flex items-center justify-between p-4 border border-gray-200 rounded-lg bg-gray-50"
            >
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <Bed className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-sm">{block.room_name}</span>
                  <Badge variant="outline" className="text-xs">
                    {block.config_name}
                  </Badge>
                </div>

                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>
                      {formatDate(block.check_in)} –{" "}
                      {formatDate(block.check_out)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span>
                      ({block.nights} night{block.nights !== 1 ? "s" : ""})
                    </span>
                  </div>
                </div>

                <div className="flex items-center gap-2 mt-2">
                  <span className="text-sm text-green-600 font-medium">
                    {block.nights} Night{block.nights !== 1 ? 's' : ''} - {formatCurrency(block.total)}
                  </span>
                </div>
              </div>



              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => onRemoveBlock(block.id)}
                className="text-red-600 hover:text-red-800 hover:bg-red-50"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>

        {/* Summary Totals */}
        <div className="border-t pt-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Total Nights:</span>
              <span className="font-medium">{totalNights}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Total Rooms:</span>
              <span className="font-medium">{roomBlocks.length}</span>
            </div>
            <div className="flex justify-between text-lg font-semibold border-t pt-2">
              <span>Total Amount:</span>
              <span className="text-green-600">
                {formatCurrency(totalAmount)}
              </span>
            </div>
          </div>
        </div>

        {/* Action Button */}
        {onAddToQuotation && (
          <div className="pt-4 border-t">
            <Button
              type="button"
              onClick={onAddToQuotation}
              className="w-full"
              disabled={roomBlocks.length === 0 || isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "💾 Save room Booking"
              )}
            </Button>
          </div>
        )}

        {/* Booking Details */}
        <div className="text-xs text-gray-500 space-y-1">
          <p>• Room rates are per night and subject to availability</p>
          <p>• Final pricing may include taxes and fees</p>
          <p>• Bookings are subject to hotel terms and conditions</p>
        </div>
      </CardContent>
    </Card>
  );
}

// Helper component for displaying individual room block details
export function RoomBlockCard({
  block,
  onRemove,
  showRemove = true,
  currency = "GBP",
}: {
  block: RoomBlock;
  onRemove?: (blockId: string) => void;
  showRemove?: boolean;
  currency?: string;
}) {
  const formatDate = (dateStr: string) => {
    return format(new Date(dateStr), "MMM d");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-GB", {
      style: "currency",
      currency: currency,
    }).format(amount);
  };

  return (
    <div className="flex items-center justify-between p-3 border border-gray-200 rounded-md bg-white">
      <div className="flex-1">
        <div className="flex items-center gap-2 mb-1">
          <Bed className="h-4 w-4 text-gray-500" />
          <span className="font-medium text-sm">{block.room_name}</span>
          <Badge variant="outline" className="text-xs">
            {block.config_name}
          </Badge>
        </div>

        <div className="text-xs text-gray-600">
          {formatDate(block.check_in)} – {formatDate(block.check_out)} (
          {block.nights} nights)
        </div>

        <div className="text-xs text-green-600 font-medium mt-1">
          {formatCurrency(block.total)}
        </div>
      </div>

      {showRemove && onRemove && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => onRemove(block.id)}
          className="text-red-600 hover:text-red-800 hover:bg-red-50 ml-2"
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
}
