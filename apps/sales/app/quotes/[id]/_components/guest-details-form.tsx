"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@flinkk/components/ui/card";
import { NumberFieldFormElement } from "@flinkk/dynamic-form/form-elements";
import { Control, useWatch } from "react-hook-form";
import { Users, Baby, AlertTriangle } from "lucide-react";

interface GuestDetailsFormProps {
  control: Control<any>;
  className?: string;
  capacityErrors?: string[];
  capacityWarnings?: string[];
}

export function GuestDetailsForm({
  control,
  className = "",
  capacityErrors = [],
  capacityWarnings = []
}: GuestDetailsFormProps) {
  // Watch form values for real-time validation feedback
  const adults = useWatch({ control, name: "adults" }) || 0;
  const children = useWatch({ control, name: "children" }) || 0;
  const totalGuests = adults + children;
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <Users className="h-5 w-5" />
          Guest Details
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <NumberFieldFormElement
            name="adults"
            label="Adults"
            control={control}
            placeholder="Number of adults"
            required={true}
            min={1}
            max={10}
            helperText="Minimum 1 adult required"
          />
          <NumberFieldFormElement
            name="children"
            label="Children"
            control={control}
            placeholder="Number of children"
            required={false}
            min={0}
            max={10}
            helperText="Optional - specify number of children"
          />
        </div>
        {/* Capacity Error Messages */}
        {capacityErrors.length > 0 && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-red-800 mb-1">Guest Capacity Issues:</p>
                <ul className="text-sm text-red-700 space-y-1">
                  {capacityErrors.map((error, index) => (
                    <li key={index} className="flex items-start gap-1">
                      <span className="text-red-500">•</span>
                      <span>{error}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* Capacity Warning Messages */}
        {capacityWarnings.length > 0 && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="flex-1">
                <p className="text-sm font-medium text-yellow-800 mb-1">Capacity Warnings:</p>
                <ul className="text-sm text-yellow-700 space-y-1">
                  {capacityWarnings.map((warning, index) => (
                    <li key={index} className="flex items-start gap-1">
                      <span className="text-yellow-500">•</span>
                      <span>{warning}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        )}

        {/* General Info Message */}
        {capacityErrors.length === 0 && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2">
              <Baby className="h-4 w-4 text-blue-600" />
              <div className="flex-1">
                <p className="text-sm text-blue-700">
                  Room availability and pricing may vary based on guest count and room capacity
                </p>
                {totalGuests > 0 && (
                  <p className="text-xs text-blue-600 mt-1">
                    Current selection: {adults} adult{adults !== 1 ? 's' : ''}{children > 0 && `, ${children} child${children !== 1 ? 'ren' : ''}`} (Total: {totalGuests})
                  </p>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
