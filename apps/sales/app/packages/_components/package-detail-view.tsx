"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { Badge } from "@flinkk/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@flinkk/components/ui/table";
import { Separator } from "@flinkk/components/ui/separator";
import {
  EditIcon,
  TrashIcon,
  ArrowLeftIcon,
  PackageIcon,
  UserIcon,
  CalendarIcon,
  DollarSignIcon,
  TagIcon,
  BuildingIcon,
} from "lucide-react";
import { formatDistanceToNow, format } from "date-fns";
import toast from "react-hot-toast";
import type { Package, PackageStatus } from "@/types/package";

interface PackageDetailViewProps {
  packageData: Package;
}

export function PackageDetailView({ packageData }: PackageDetailViewProps) {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle package deletion
  const handleDelete = async () => {
    if (
      !confirm(
        `Are you sure you want to delete the package "${packageData.name}"?`,
      )
    ) {
      return;
    }

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/packages/${packageData.id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Package deleted successfully");
        router.push("/packages");
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to delete package");
      }
    } catch (error) {
      console.error("Error deleting package:", error);
      toast.error("Failed to delete package");
    } finally {
      setIsDeleting(false);
    }
  };

  // Status badge component
  const StatusBadge = ({ status }: { status: PackageStatus }) => {
    const variants = {
      ACTIVE: "default",
      INACTIVE: "secondary",
      DRAFT: "outline",
      ARCHIVED: "destructive",
    } as const;

    return (
      <Badge variant={variants[status] || "secondary"}>
        {status.toLowerCase()}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeftIcon className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              {packageData.name}
            </h1>
            <div className="flex items-center space-x-4 mt-1">
              <StatusBadge status={packageData.status} />
              {packageData.sku && (
                <span className="text-sm text-muted-foreground">
                  SKU: {packageData.sku}
                </span>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => router.push(`/packages/${packageData.id}`)}
          >
            <EditIcon className="mr-2 h-4 w-4" />
            Edit
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={isDeleting}
          >
            <TrashIcon className="mr-2 h-4 w-4" />
            {isDeleting ? "Deleting..." : "Delete"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Package Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PackageIcon className="mr-2 h-5 w-5" />
                Package Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {packageData.description && (
                <div>
                  <h4 className="font-medium mb-2">Description</h4>
                  <p className="text-muted-foreground">
                    {packageData.description}
                  </p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-medium mb-1">Status</h4>
                  <StatusBadge status={packageData.status} />
                </div>
                {packageData.sku && (
                  <div>
                    <h4 className="font-medium mb-1">SKU</h4>
                    <p className="text-muted-foreground">{packageData.sku}</p>
                  </div>
                )}
                {packageData.preferredVendor && (
                  <div>
                    <h4 className="font-medium mb-1">Preferred Vendor</h4>
                    <p className="text-muted-foreground">
                      {packageData.preferredVendor}
                    </p>
                  </div>
                )}
                <div>
                  <h4 className="font-medium mb-1">Total Items</h4>
                  <p className="text-muted-foreground">
                    {packageData.lines?.length || 0} items
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Line Items */}
          <Card>
            <CardHeader>
              <CardTitle>Package Contents</CardTitle>
            </CardHeader>
            <CardContent>
              {packageData.lines && packageData.lines.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product/Service</TableHead>
                      <TableHead className="text-center">Qty</TableHead>
                      <TableHead className="text-center">Unit</TableHead>
                      <TableHead className="text-right">Unit Price</TableHead>
                      <TableHead className="text-right">Discount</TableHead>
                      <TableHead className="text-right">Line Total</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {packageData.lines.map((line) => (
                      <TableRow key={line.id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">
                              {line.description}
                            </div>
                            {line.product && (
                              <div className="text-sm text-muted-foreground">
                                {line.product.name}{" "}
                                {line.product.sku && `(${line.product.sku})`}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-center">
                          {line.quantity}
                        </TableCell>
                        <TableCell className="text-center">
                          {line.unit ? line.unit.displayName : "-"}
                        </TableCell>
                        <TableCell className="text-right">
                          ${line.unitPrice.toFixed(2)}
                        </TableCell>
                        <TableCell className="text-right">
                          {line.discount > 0 ? (
                            <span className="text-red-600">
                              {line.discountType === "PERCENTAGE"
                                ? `${line.discountValue}%`
                                : `$${line.discountValue}`}
                            </span>
                          ) : (
                            "-"
                          )}
                        </TableCell>
                        <TableCell className="text-right font-medium">
                          ${line.lineTotal.toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  No line items found in this package.
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Pricing Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                Pricing Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span>Subtotal:</span>
                <span>${packageData.subtotal.toFixed(2)}</span>
              </div>

              {packageData.packageDiscount > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>Package Discount:</span>
                  <span>-${packageData.packageDiscount.toFixed(2)}</span>
                </div>
              )}

              <div className="flex justify-between">
                <span>After Discount:</span>
                <span>${packageData.totalAfterDiscount.toFixed(2)}</span>
              </div>

              {packageData.totalTax > 0 && (
                <div className="flex justify-between">
                  <span>Tax ({packageData.taxPercentage}%):</span>
                  <span>${packageData.totalTax.toFixed(2)}</span>
                </div>
              )}

              <Separator />

              <div className="flex justify-between font-medium text-lg">
                <span>Grand Total:</span>
                <span>${packageData.grandTotal.toFixed(2)}</span>
              </div>

              {packageData.manualPriceOverride && (
                <div className="text-xs text-muted-foreground">
                  * Manual price override applied
                </div>
              )}
            </CardContent>
          </Card>

          {/* Package Details */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {packageData.createdBy && (
                <div className="flex items-center space-x-2">
                  <UserIcon className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Created by</div>
                    <div className="text-sm text-muted-foreground">
                      {packageData.createdBy.name}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                <div>
                  <div className="text-sm font-medium">Created</div>
                  <div className="text-sm text-muted-foreground">
                    {format(new Date(packageData.createdAt), "PPP")}
                    <br />
                    <span className="text-xs">
                      {formatDistanceToNow(new Date(packageData.createdAt), {
                        addSuffix: true,
                      })}
                    </span>
                  </div>
                </div>
              </div>

              {packageData.updatedAt !== packageData.createdAt && (
                <div className="flex items-center space-x-2">
                  <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Last updated</div>
                    <div className="text-sm text-muted-foreground">
                      {format(new Date(packageData.updatedAt), "PPP")}
                      <br />
                      <span className="text-xs">
                        {formatDistanceToNow(new Date(packageData.updatedAt), {
                          addSuffix: true,
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {packageData.preferredVendor && (
                <div className="flex items-center space-x-2">
                  <BuildingIcon className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">Preferred Vendor</div>
                    <div className="text-sm text-muted-foreground">
                      {packageData.preferredVendor}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
