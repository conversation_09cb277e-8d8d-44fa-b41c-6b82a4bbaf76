"use client";

import { Card, CardContent } from "@flinkk/components/ui/card";
import { Button } from "@flinkk/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@flinkk/components/ui/dropdown-menu";
import {
  MoreHorizontalIcon,
  BuildingIcon,
  DollarSignIcon,
  CalendarIcon,
  UserIcon,
} from "lucide-react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";

export const OpportunityCard = ({
  opportunity,
  stage,
  isDragging,
  setIsDragging,
  handleDrop,
  handleDragStart,
  draggedOpportunity,
}: any) => {
  const router = useRouter();

  // Handle view opportunity detail
  const handleViewOpportunityDetail = (opportunityId: string) => {
    router.push(`/opportunities/${opportunityId}/view`);
  };

  return (
    <Card
      key={opportunity.id}
      className={`cursor-pointer hover:border-primary py-2 ${
        isDragging && draggedOpportunity?.id === opportunity.id
          ? "opacity-50"
          : ""
      }`}
      draggable
      onDragStart={() => handleDragStart(opportunity)}
      onDragEnd={() => setIsDragging(false)}
      onClick={() => handleViewOpportunityDetail(opportunity.id)}
    >
      <CardContent className="p-3">
        <div className="flex justify-between items-start mb-2">
          <h4 className="font-medium text-sm line-clamp-2">
            {opportunity.name}
          </h4>
          <DropdownMenu>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <MoreHorizontalIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation();
                  handleViewOpportunityDetail(opportunity.id);
                }}
              >
                View Details
              </DropdownMenuItem>
              {stage.stage !== "CLOSED_WON" && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDrop(e as any, "CLOSED_WON");
                  }}
                >
                  Mark as Won
                </DropdownMenuItem>
              )}
              {stage.stage !== "CLOSED_LOST" && (
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDrop(e as any, "CLOSED_LOST");
                  }}
                >
                  Mark as Lost
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-1 text-xs text-muted-foreground">
          <div className="flex items-center">
            <BuildingIcon className="h-3 w-3 mr-1" />
            {opportunity.account?.name || "No account"}
          </div>

          <div className="flex items-center">
            {formatCurrency(opportunity.amount)}
          </div>

          <div className="flex items-center">
            <CalendarIcon className="h-3 w-3 mr-1" />
            {formatDate(opportunity.closeDate)}
          </div>

          <div className="flex items-center">
            <UserIcon className="h-3 w-3 mr-1" />
            {opportunity.user?.name || "Unassigned"}
          </div>
        </div>

        {opportunity.probability !== null &&
          opportunity.probability !== undefined && (
            <div className="mt-2">
              <div className="flex justify-between text-xs mb-1">
                <span>Probability</span>
                <span>{opportunity.probability}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-1.5">
                <div
                  className="bg-primary rounded-full h-1.5"
                  style={{
                    width: `${opportunity.probability}%`,
                  }}
                ></div>
              </div>
            </div>
          )}
      </CardContent>
    </Card>
  );
};

// Format date
const formatDate = (dateString: string | null | undefined) => {
  if (!dateString) return "No date";

  return format(new Date(dateString), "MMM d, yyyy");
};

// Format currency
const formatCurrency = (value: number | null | undefined) => {
  if (value === null || value === undefined) return "N/A";

  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
};
