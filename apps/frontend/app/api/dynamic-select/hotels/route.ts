import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";

export const dynamic = "force-dynamic";

// POST /api/dynamic-select/hotels - Get hotels from inventory service
export async function POST(req: NextRequest) {
  try {
    const { tenantId } = await getToken({ req });

    // Get inventory configuration for the tenant
    const inventoryConfig = await prisma.inventoryConfiguration.findUnique({
      where: {
        tenantId,
      },
      select: {
        apiUrl: true,
        token: true,
        isActive: true,
        verificationStatus: true,
      },
    });

    // Check if inventory is configured and active
    if (!inventoryConfig || !inventoryConfig.apiUrl || !inventoryConfig.token) {
      return NextResponse.json([]);
    }

    // Parse request body for search parameters
    const body = await req.json();
    const { search = "", destination_id = "" } = body || {};

    // Create inventory API instance
    const inventoryAPI = new FlinkkInventoryAPI({
      apiUrl: inventoryConfig.apiUrl,
      token: inventoryConfig.token,
    });

    // Get hotels using the API with destination filtering
    const hotels = await inventoryAPI.getHotels({
      search: search || undefined,
      destination_id: destination_id || undefined,
      limit: 50,
    });

    console.log({ hotels });

    // Transform hotels to DynamicSelect option format
    // Handle both array response and object with hotels property
    const hotelsList = Array.isArray(hotels) ? hotels : hotels.hotels || [];
    const options = hotelsList.map((hotel: any) => ({
      value: hotel.id || hotel._id,
      label: hotel.name || hotel.title,
      location: hotel.location || hotel.address,
      description: hotel.description,
    }));

    // If destination_id is provided but no hotels found, return empty array
    // The frontend will handle showing appropriate message
    if (destination_id && options.length === 0) {
      console.log(`No hotels found for destination_id: ${destination_id}`);
    }

    return NextResponse.json(options);
  } catch (error) {
    console.error("Error fetching hotels from inventory service:", error);
    return NextResponse.json([]);
  }
}
