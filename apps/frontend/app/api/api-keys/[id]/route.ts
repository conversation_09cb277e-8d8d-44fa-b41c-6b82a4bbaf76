import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@flinkk/database/prisma";
import { getServerSession } from "@flinkk/shared-auth/options";
import { withTenantRBAC } from "@flinkk/shared-rbac";
import { z } from "zod";

// Validation schema for API key updates
const updateApiKeySchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name too long")
    .optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  permissions: z
    .object({
      tickets: z
        .object({
          create: z.boolean().default(false),
          read: z.boolean().default(false),
          update: z.boolean().default(false),
          delete: z.boolean().default(false),
        })
        .default({}),
      contacts: z
        .object({
          create: z.boolean().default(false),
          read: z.boolean().default(false),
          update: z.boolean().default(false),
          delete: z.boolean().default(false),
        })
        .default({}),
      emails: z
        .object({
          send: z.boolean().default(false),
          read: z.boolean().default(false),
          list: z.boolean().default(false),
        })
        .default({}),
      webhooks: z
        .object({
          manage: z.boolean().default(false),
        })
        .default({}),
    })
    .optional(),
  expiresAt: z.string().datetime().optional(),
  rateLimit: z.number().min(1).max(10000).optional(),
  ipWhitelist: z.array(z.string()).optional(),
});

/**
 * GET /api/api-keys/[id] - Get a specific API key
 */
export const GET = withTenantRBAC(
  async (
    req: NextRequest,
    rbacPrisma: any,
    tenantId: string,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const resolvedParams = await params;
      const { id } = resolvedParams;

      const apiKey = await prisma.apiKey.findFirst({
        where: { id, tenantId },
        select: {
          id: true,
          name: true,
          description: true,
          keyPrefix: true,
          isActive: true,
          lastUsedAt: true,
          expiresAt: true,
          permissions: true,
          rateLimit: true,
          ipWhitelist: true,
          createdAt: true,
          updatedAt: true,
          createdBy: {
            select: {
              name: true,
              email: true,
            },
          },
          updatedBy: {
            select: {
              name: true,
              email: true,
            },
          },
        },
      });

      if (!apiKey) {
        return NextResponse.json(
          { error: "API key not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({ apiKey });
    } catch (error) {
      console.error("Error fetching API key:", error);
      return NextResponse.json(
        { error: "Failed to fetch API key" },
        { status: 500 }
      );
    }
  }
);

/**
 * PUT /api/api-keys/[id] - Update a specific API key
 */
export const PUT = withTenantRBAC(
  async (
    req: NextRequest,
    rbacPrisma: any,
    tenantId: string,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const { userId } = await getServerSession();
      const resolvedParams = await params;
      const { id } = resolvedParams;
      const body = await req.json();

      const validatedData = updateApiKeySchema.parse(body);

      // Check if API key exists and belongs to tenant
      const existingApiKey = await prisma.apiKey.findFirst({
        where: { id, tenantId },
      });

      if (!existingApiKey) {
        return NextResponse.json(
          { error: "API key not found" },
          { status: 404 }
        );
      }

      // Update the API key
      const updatedApiKey = await prisma.apiKey.update({
        where: { id },
        data: {
          ...validatedData,
          expiresAt: validatedData.expiresAt
            ? new Date(validatedData.expiresAt)
            : undefined,
          updatedById: userId,
        },
        select: {
          id: true,
          name: true,
          description: true,
          keyPrefix: true,
          isActive: true,
          lastUsedAt: true,
          expiresAt: true,
          permissions: true,
          rateLimit: true,
          ipWhitelist: true,
          createdAt: true,
          updatedAt: true,
          createdBy: {
            select: {
              name: true,
              email: true,
            },
          },
          updatedBy: {
            select: {
              name: true,
              email: true,
            },
          },
        },
      });

      return NextResponse.json({ apiKey: updatedApiKey });
    } catch (error) {
      console.error("Error updating API key:", error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            error: "Validation failed",
            details: error.errors,
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: "Failed to update API key" },
        { status: 500 }
      );
    }
  }
);

/**
 * DELETE /api/api-keys/[id] - Delete a specific API key
 */
export const DELETE = withTenantRBAC(
  async (
    req: NextRequest,
    rbacPrisma: any,
    tenantId: string,
    { params }: { params: Promise<{ id: string }> }
  ) => {
    try {
      const resolvedParams = await params;
      const { id } = resolvedParams;

      // Check if API key exists and belongs to tenant
      const existingApiKey = await prisma.apiKey.findFirst({
        where: { id, tenantId },
      });

      if (!existingApiKey) {
        return NextResponse.json(
          { error: "API key not found" },
          { status: 404 }
        );
      }

      // Delete the API key
      await prisma.apiKey.delete({
        where: { id },
      });

      return NextResponse.json({ success: true });
    } catch (error) {
      console.error("Error deleting API key:", error);
      return NextResponse.json(
        { error: "Failed to delete API key" },
        { status: 500 }
      );
    }
  }
);
