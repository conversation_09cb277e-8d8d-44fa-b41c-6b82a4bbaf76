// Debug script to test guest-based pricing logic
// Run this in the browser console when on a quote page with hotel booking

// Sample pricing data structure for testing
const samplePricingData = {
  success: true,
  hotel: {
    id: "hotel_1",
    name: "Test Hotel",
    handle: "test-hotel"
  },
  pricing: {
    base_price_rules: [
      {
        id: "rule_1",
        room_config_id: "room_1",
        occupancy_type_id: "occ_1",
        amount: "15000", // £150.00 in pence
        monday_price: "12000", // £120.00 in pence
        tuesday_price: "13000", // £130.00 in pence
        wednesday_price: "14000", // £140.00 in pence
        thursday_price: "15000", // £150.00 in pence
        friday_price: "18000", // £180.00 in pence
        saturday_price: "20000", // £200.00 in pence
        sunday_price: "16000", // £160.00 in pence
        description: "Base Adult Rate"
      },
      {
        id: "rule_2",
        room_config_id: "room_1",
        occupancy_type_id: "occ_2",
        amount: "25000", // £250.00 in pence
        monday_price: "22000", // £220.00 in pence
        tuesday_price: "23000", // £230.00 in pence
        wednesday_price: "24000", // £240.00 in pence
        thursday_price: "25000", // £250.00 in pence
        friday_price: "28000", // £280.00 in pence
        saturday_price: "30000", // £300.00 in pence
        sunday_price: "26000", // £260.00 in pence
        description: "Base 2 Person Rate"
      },
      {
        id: "rule_3",
        room_config_id: "room_1",
        occupancy_type_id: "occ_3",
        amount: "5000", // £50.00 in pence
        monday_price: "4000", // £40.00 in pence
        tuesday_price: "4500", // £45.00 in pence
        wednesday_price: "5000", // £50.00 in pence
        thursday_price: "5500", // £55.00 in pence
        friday_price: "6000", // £60.00 in pence
        saturday_price: "7000", // £70.00 in pence
        sunday_price: "5000", // £50.00 in pence
        description: "Extra Adult Rate"
      },
      {
        id: "rule_4",
        room_config_id: "room_1",
        occupancy_type_id: "occ_4",
        amount: "2000", // £20.00 in pence
        monday_price: "1500", // £15.00 in pence
        tuesday_price: "1800", // £18.00 in pence
        wednesday_price: "2000", // £20.00 in pence
        thursday_price: "2200", // £22.00 in pence
        friday_price: "2500", // £25.00 in pence
        saturday_price: "3000", // £30.00 in pence
        sunday_price: "2000", // £20.00 in pence
        description: "Child Rate"
      }
    ],
    seasonal_price_rules: [],
    occupancy_configs: [
      {
        id: "occ_1",
        name: "BASE_1",
        title: "1 Person (Base)",
        description: "First adult occupancy",
        min_occupancy: 1,
        max_occupancy: 1,
        is_active: true
      },
      {
        id: "occ_2",
        name: "BASE_2",
        title: "2 Persons (Base)",
        description: "Two person base occupancy",
        min_occupancy: 2,
        max_occupancy: 2,
        is_active: true
      },
      {
        id: "occ_3",
        name: "EXTRA_ADULT",
        title: "Extra Adult",
        description: "Additional adult occupancy",
        min_occupancy: 1,
        max_occupancy: 10,
        is_active: true
      },
      {
        id: "occ_4",
        name: "CHILD",
        title: "Child",
        description: "Child occupancy",
        min_occupancy: 0,
        max_occupancy: 10,
        is_active: true
      }
    ],
    meal_plans: [],
    currency_code: "GBP",
    store_context: true
  }
};

// Test function
function testGuestBasedPricing() {
  console.log("🧪 Starting guest-based pricing debug test");
  
  // Test scenarios
  const testCases = [
    {
      name: "1 Adult, 0 Children - Monday (BASE_1)",
      guestDetails: { adults: 1, children: 0 },
      date: "2025-01-06", // Monday
      expected: { base: 120, extraAdults: 0, children: 0, total: 120 }
    },
    {
      name: "2 Adults, 0 Children - Friday (BASE_2)",
      guestDetails: { adults: 2, children: 0 },
      date: "2025-01-10", // Friday
      expected: { base: 280, extraAdults: 0, children: 0, total: 280 }
    },
    {
      name: "2 Adults, 1 Child - Saturday (BASE_2 + CHILD)",
      guestDetails: { adults: 2, children: 1 },
      date: "2025-01-11", // Saturday
      expected: { base: 300, extraAdults: 0, children: 30, total: 330 }
    },
    {
      name: "3 Adults, 0 Children - Friday (BASE_1 + EXTRA_ADULT)",
      guestDetails: { adults: 3, children: 0 },
      date: "2025-01-10", // Friday
      expected: { base: 180, extraAdults: 120, children: 0, total: 300 }
    }
  ];
  
  testCases.forEach(testCase => {
    console.log(`\n🧪 Testing: ${testCase.name}`);
    
    if (typeof window !== 'undefined' && window.debugGuestPricing) {
      const result = window.debugGuestPricing("room_1", testCase.date);
      console.log("Result:", result);
      
      if (result) {
        console.log(`Expected: ${testCase.expected.total}, Got: ${result.totalPrice}`);
        console.log(`Breakdown - Base: ${result.breakdown.base}, Extra: ${result.breakdown.extraAdults}, Children: ${result.breakdown.children}`);
      }
    } else {
      console.log("❌ Debug function not available. Make sure you're on a quote page with hotel booking.");
    }
  });
}

// Instructions
console.log(`
🔧 DEBUGGING INSTRUCTIONS:
1. Navigate to a quote page with hotel booking functionality
2. Open browser console
3. Run: testGuestBasedPricing()
4. Check the debug logs for pricing calculation details

Or manually test with:
- window.debugPricingData() - to see current pricing data
- window.debugGuestPricing("room_config_id", "2025-01-06") - to test specific room and date
`);

// Auto-run if debug functions are available
if (typeof window !== 'undefined' && window.debugGuestPricing) {
  testGuestBasedPricing();
}
